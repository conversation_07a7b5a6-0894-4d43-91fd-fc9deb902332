package co.metode.hamim.ebook.reader

import android.app.ProgressDialog
import android.os.Bundle
import android.util.Log
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import co.metode.hamim.R
import co.metode.hamim.databinding.ActivityBookReaderBinding
import com.github.barteksc.pdfviewer.PDFView
import com.github.barteksc.pdfviewer.listener.OnLoadCompleteListener
import com.github.barteksc.pdfviewer.listener.OnPageChangeListener
import com.github.barteksc.pdfviewer.listener.OnPageErrorListener
import com.github.barteksc.pdfviewer.scroll.DefaultScrollHandle
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.ResponseBody
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.util.concurrent.TimeUnit

/**
 * Activity for reading PDF books
 */
class BookReaderActivity : AppCompatActivity(), OnPageChangeListener, OnLoadCompleteListener, OnPageErrorListener {

    private lateinit var binding: ActivityBookReaderBinding

    private var bookId: String? = null
    private var bookTitle: String? = null
    private var pdfPath: String? = null
    private var pdfUrl: String? = null

    private var currentPage = 0
    private var totalPages = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBookReaderBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Set up toolbar
        setSupportActionBar(binding.toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.setDisplayShowHomeEnabled(true)

        // Get data from intent
        bookId = intent.getStringExtra("book_id")
        bookTitle = intent.getStringExtra("book_title")
        pdfPath = intent.getStringExtra("pdf_path")
        pdfUrl = intent.getStringExtra("pdf_url")

        // Set title
        supportActionBar?.title = bookTitle ?: "PDF Reader"

        // Set up UI listeners
        setupListeners()

        // Load PDF
        loadPdf()
    }

    private fun setupListeners() {
        // Navigation buttons
        binding.prevPageButton.setOnClickListener {
            if (currentPage > 0) {
                binding.pdfView.jumpTo(currentPage - 1)
            }
        }

        binding.nextPageButton.setOnClickListener {
            if (currentPage < totalPages - 1) {
                binding.pdfView.jumpTo(currentPage + 1)
            }
        }
    }

    private fun loadPdf() {
        binding.progressBar.visibility = View.VISIBLE

        try {
            when {
                // Load from local file path
                !pdfPath.isNullOrEmpty() -> {
                    val file = File(pdfPath!!)
                    if (file.exists()) {
                        displayFromFile(file)
                    } else {
                        showPdfNotFoundError()
                    }
                }

                // Load from assets (sample book)
                bookId == "sample_book" -> {
                    displayFromAsset(Constants.DEFAULT_SAMPLE_PDF)
                }

                // Load from URL
                !pdfUrl.isNullOrEmpty() -> {
                    // Download the PDF from URL and then display it
                    downloadAndDisplayPdf(pdfUrl!!)
                }

                // No source provided
                else -> {
                    showPdfNotFoundError()
                }
            }
        } catch (e: Exception) {
            Log.e("BookReaderActivity", "Error loading PDF", e)
            showPdfNotFoundError(e.message ?: "Unknown error")
        }
    }

    private fun displayFromFile(file: File) {
        binding.pdfView.fromFile(file)
            .defaultPage(0)
            .onPageChange(this)
            .enableAnnotationRendering(true)
            .onLoad(this)
            .onPageError(this)
            .scrollHandle(DefaultScrollHandle(this))
            .spacing(10) // in dp
            .load()
    }

    private fun displayFromAsset(assetName: String) {
        binding.pdfView.fromAsset(assetName)
            .defaultPage(0)
            .onPageChange(this)
            .enableAnnotationRendering(true)
            .onLoad(this)
            .onPageError(this)
            .scrollHandle(DefaultScrollHandle(this))
            .spacing(10) // in dp
            .load()
    }

    private fun showPdfNotFoundError(message: String = "PDF file not found") {
        binding.pdfView.visibility = View.GONE
        binding.controlsLayout.visibility = View.GONE
        binding.progressBar.visibility = View.GONE
        binding.pdfNotFoundLayout.visibility = View.VISIBLE
        binding.pdfNotFoundMessage.text = message
    }

    private fun updatePageCounter() {
        binding.pageCounter.text = getString(R.string.page_counter_format, currentPage + 1, totalPages)
    }

    // OnPageChangeListener
    override fun onPageChanged(page: Int, pageCount: Int) {
        currentPage = page
        totalPages = pageCount
        updatePageCounter()
    }

    // OnLoadCompleteListener
    override fun loadComplete(nbPages: Int) {
        binding.progressBar.visibility = View.GONE
        totalPages = nbPages
        updatePageCounter()
    }

    // OnPageErrorListener
    override fun onPageError(page: Int, t: Throwable?) {
        Log.e("BookReaderActivity", "Error loading page $page", t)
        binding.progressBar.visibility = View.GONE
        Toast.makeText(this, "Error loading page $page", Toast.LENGTH_SHORT).show()
    }

    /**
     * Downloads a PDF from a URL and displays it
     */
    private fun downloadAndDisplayPdf(url: String) {
        binding.progressBar.visibility = View.VISIBLE

        // Show a progress dialog while downloading
        val progressDialog = ProgressDialog(this).apply {
            setMessage("Downloading PDF...")
            setProgressStyle(ProgressDialog.STYLE_HORIZONTAL)
            setCancelable(false)
            max = 100
            show()
        }

        // Create a temporary file to store the downloaded PDF
        val tempFile = File(cacheDir, "temp_${System.currentTimeMillis()}.pdf")

        // Run the download in a background thread
        Thread {
            try {
                // Set up OkHttp client with timeouts
                val client = OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .build()

                // Create the request
                val request = Request.Builder()
                    .url(url)
                    .build()

                // Execute the request
                val response = client.newCall(request).execute()

                if (!response.isSuccessful) {
                    throw IOException("Failed to download PDF: ${response.code}")
                }

                // Get the response body
                val responseBody = response.body

                if (responseBody == null) {
                    throw IOException("Empty response body")
                }

                // Save the response body to the temporary file
                val success = saveResponseBodyToFile(responseBody, tempFile, progressDialog)

                // Update UI on the main thread
                runOnUiThread {
                    progressDialog.dismiss()

                    if (success) {
                        // Display the downloaded PDF
                        displayFromFile(tempFile)
                    } else {
                        showPdfNotFoundError("Failed to download PDF")
                    }
                }
            } catch (e: Exception) {
                // Handle any errors
                Log.e("BookReaderActivity", "Error downloading PDF", e)

                // Update UI on the main thread
                runOnUiThread {
                    progressDialog.dismiss()
                    showPdfNotFoundError("Error downloading PDF: ${e.message}")
                }
            }
        }.start()
    }

    /**
     * Saves a response body to a file with progress updates
     */
    private fun saveResponseBodyToFile(body: ResponseBody, file: File, progressDialog: ProgressDialog): Boolean {
        try {
            var inputStream: InputStream? = null
            var outputStream: FileOutputStream? = null

            try {
                val fileReader = ByteArray(4096)
                val fileSize = body.contentLength()
                var fileSizeDownloaded: Long = 0

                inputStream = body.byteStream()
                outputStream = FileOutputStream(file)

                while (true) {
                    val read = inputStream.read(fileReader)
                    if (read == -1) break

                    outputStream.write(fileReader, 0, read)
                    fileSizeDownloaded += read

                    // Update progress
                    val progress = ((fileSizeDownloaded * 100) / fileSize).toInt()
                    runOnUiThread {
                        progressDialog.progress = progress
                    }
                }

                outputStream.flush()
                return true
            } catch (e: IOException) {
                Log.e("BookReaderActivity", "Error saving PDF", e)
                return false
            } finally {
                inputStream?.close()
                outputStream?.close()
            }
        } catch (e: IOException) {
            Log.e("BookReaderActivity", "Error creating PDF file", e)
            return false
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
